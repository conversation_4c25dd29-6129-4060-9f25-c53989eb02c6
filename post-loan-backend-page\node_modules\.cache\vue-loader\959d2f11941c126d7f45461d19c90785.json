{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754038828240}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL2xpdGlnYXRpb25fYXBwcm92YWwudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTdjZDMxZjM4JnNjb3BlZD10cnVlIgppbXBvcnQgc2NyaXB0IGZyb20gIi4vbGl0aWdhdGlvbl9hcHByb3ZhbC52dWU/dnVlJnR5cGU9c2NyaXB0Jmxhbmc9anMiCmV4cG9ydCAqIGZyb20gIi4vbGl0aWdhdGlvbl9hcHByb3ZhbC52dWU/dnVlJnR5cGU9c2NyaXB0Jmxhbmc9anMiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9saXRpZ2F0aW9uX2FwcHJvdmFsLnZ1ZT92dWUmdHlwZT1zdHlsZSZpbmRleD0wJmlkPTdjZDMxZjM4JnNjb3BlZD10cnVlJmxhbmc9Y3NzIgoKCi8qIG5vcm1hbGl6ZSBjb21wb25lbnQgKi8KaW1wb3J0IG5vcm1hbGl6ZXIgZnJvbSAiIS4uLy4uLy4uLy4uL25vZGVfbW9kdWxlcy92dWUtbG9hZGVyL2xpYi9ydW50aW1lL2NvbXBvbmVudE5vcm1hbGl6ZXIuanMiCnZhciBjb21wb25lbnQgPSBub3JtYWxpemVyKAogIHNjcmlwdCwKICByZW5kZXIsCiAgc3RhdGljUmVuZGVyRm5zLAogIGZhbHNlLAogIG51bGwsCiAgIjdjZDMxZjM4IiwKICBudWxsCiAgCikKCi8qIGhvdCByZWxvYWQgKi8KaWYgKG1vZHVsZS5ob3QpIHsKICB2YXIgYXBpID0gcmVxdWlyZSgiRDpcXGNvZGVfcHJvamVjdFxcamF2YV9wcm9qZWN0XFxsb2FuXFxwb3N0LWxvYW4tYmFja2VuZC1wYWdlXFxub2RlX21vZHVsZXNcXHZ1ZS1ob3QtcmVsb2FkLWFwaVxcZGlzdFxcaW5kZXguanMiKQogIGFwaS5pbnN0YWxsKHJlcXVpcmUoJ3Z1ZScpKQogIGlmIChhcGkuY29tcGF0aWJsZSkgewogICAgbW9kdWxlLmhvdC5hY2NlcHQoKQogICAgaWYgKCFhcGkuaXNSZWNvcmRlZCgnN2NkMzFmMzgnKSkgewogICAgICBhcGkuY3JlYXRlUmVjb3JkKCc3Y2QzMWYzOCcsIGNvbXBvbmVudC5vcHRpb25zKQogICAgfSBlbHNlIHsKICAgICAgYXBpLnJlbG9hZCgnN2NkMzFmMzgnLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0KICAgIG1vZHVsZS5ob3QuYWNjZXB0KCIuL2xpdGlnYXRpb25fYXBwcm92YWwudnVlP3Z1ZSZ0eXBlPXRlbXBsYXRlJmlkPTdjZDMxZjM4JnNjb3BlZD10cnVlIiwgZnVuY3Rpb24gKCkgewogICAgICBhcGkucmVyZW5kZXIoJzdjZDMxZjM4JywgewogICAgICAgIHJlbmRlcjogcmVuZGVyLAogICAgICAgIHN0YXRpY1JlbmRlckZuczogc3RhdGljUmVuZGVyRm5zCiAgICAgIH0pCiAgICB9KQogIH0KfQpjb21wb25lbnQub3B0aW9ucy5fX2ZpbGUgPSAic3JjL3ZpZXdzL2xpdGlnYXRpb24vbGl0aWdhdGlvbi9saXRpZ2F0aW9uX2FwcHJvdmFsLnZ1ZSIKZXhwb3J0IGRlZmF1bHQgY29tcG9uZW50LmV4cG9ydHM="}]}